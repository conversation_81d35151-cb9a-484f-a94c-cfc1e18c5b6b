<odoo>
    <data noupdate="1">

        <!-- Create default journals for hotel payments -->
        <record id="hotel_cash_journal" model="account.journal">
            <field name="name">Hotel Cash</field>
            <field name="code">HCASH</field>
            <field name="type">cash</field>
            <field name="company_id">1</field>
        </record>

        <record id="hotel_bank_journal" model="account.journal">
            <field name="name">Hotel Bank</field>
            <field name="code">HBANK</field>
            <field name="type">bank</field>
            <field name="company_id">1</field>
        </record>

        <!--======= Hotel Payment method======= -->
        <record id="payment_method_mobileBanking" model="hotel.payment.method">
            <field name="payment_type">Mobile Banking</field>
            <field name="company_id">1</field>
            <field name="journal_id" ref="hotel_bank_journal"/>
        </record>
        <record id="payment_method_cash" model="hotel.payment.method">
            <field name="payment_type">Cash</field>
            <field name="company_id">1</field>
            <field name="journal_id" ref="hotel_cash_journal"/>
        </record>
        <record id="payment_methpd_digitalWallet" model="hotel.payment.method">
            <field name="payment_type">Digital Wallet</field>
            <field name="company_id">1</field>
            <field name="journal_id" ref="hotel_bank_journal"/>
        </record>
        <record id="payment_method_card" model="hotel.payment.method">
            <field name="payment_type">Card</field>
            <field name="company_id">1</field>
            <field name="journal_id" ref="hotel_bank_journal"/>
        </record>

        <!--======= Hotel Payment======= -->
        <record id="payment_mob_bank_NICA" model="hotel.payment">
            <field name="payment_type">1</field>
            <field name="payment_service_provider">NICA</field>
            <field name="payment_name_code">NICENPKA</field>
            <field name="company_id">1</field>
        </record>
        <record id="payment_esewa" model="hotel.payment">
            <field name="payment_type">3</field>
            <field name="payment_service_provider">Esewa</field>
            <field name="company_id">1</field>
        </record>
    </data>
</odoo>