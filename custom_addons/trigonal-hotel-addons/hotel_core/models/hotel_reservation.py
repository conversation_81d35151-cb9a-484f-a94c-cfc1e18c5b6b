from datetime import datetime, timedelta
from odoo import fields, _, models, api
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class HotelReservation(models.Model):
    _name = "hotel.reservation"
    _description = "Hotel reservation model"
    _order = "checkout_date desc, name desc"
    _check_company_auto = True
    _inherit = ["mail.thread", "mail.activity.mixin"]

    READONLY_STATES = {
        "confirm": [("readonly", True)],
        "checkin": [("readonly", True)],
        "checkout": [("readonly", True)],
        "done": [("readonly", True)],
        "cancel": [("readonly", True)],
    }

    name = fields.Char(
        string="Booking Reference",
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _("New"),
        tracking=True)
    company_id = fields.Many2one(
        "res.company", required=True, default=lambda self: self.env.company)
    partner_id = fields.Many2one(
        "res.partner", string="Guest", required=True, tracking=True)
    additional_guests = fields.Many2many(
        "res.partner", string="Additional Guests", tracking=True)
    total_guests = fields.Integer(string="Number of Guests", default=1, tracking=True)
    booking_date = fields.Datetime(
        "Booking Date", default=lambda self: fields.Datetime.now(), tracking=True, required=True)
    booked_by = fields.Many2one("res.users", string="Booked By", tracking=True)
    confirmed_date = fields.Datetime(
        "Confirmed Date", copy=False, default=lambda self: fields.Datetime.now(), tracking=True)
    confirmed_by = fields.Many2one("res.users", string="Confirmed By", tracking=True)
    checkin_date = fields.Datetime("Checkin Date", tracking=True, copy=False)
    checkin_by = fields.Many2one("res.users", string="Checked In By", tracking=True)
    checkout_date = fields.Datetime("Checkout Date", tracking=True)

    # Estimated Start and end date
    possible_checkin_date = fields.Datetime(
        "Possible Checkin Date", default=lambda self: fields.Datetime.now(), tracking=True, required=True)
    possible_checkout_date = fields.Datetime("Possible Checkout Date", tracking=True, required=True)

    is_locked = fields.Boolean("Lock Reservation", default=False, copy=False)
    locked_date = fields.Datetime("Locked Date", copy=False)

    checkout_by = fields.Many2one("res.users", string="Checked Out By", tracking=True)
    cancelled_date = fields.Datetime("Cancelled Date", tracking=True)
    cancelled_by = fields.Many2one("res.users", string="Cancelled By", tracking=True)
    cancellation_reason = fields.Text(string="Cancellation Reason")
    card_no = fields.Integer(string="ID Card Number")
    card_type = fields.Selection(
        [
            ("citizenship", "Citizenship"),
            ("licence", "Driving Licence"),
            ("passport", "Passport"),
            ("idcard", "ID Card"),
        ])
    document = fields.Binary("Documents")

    room_ids = fields.One2many(
        "hotel.room.reservation",
        "reservation_id",
        string="Rooms",
        required=True,
        help="Rooms",
        copy=True,
        check_company=True)
    rooms = fields.Many2one(related="room_ids.room_id", string="Rooms")
    room_display_html = fields.Html(string="Room", compute="_get_room_display_html", store=True)

    state = fields.Selection(
        [
            ("draft", "Draft"),
            ("book", "Booked"),
            ("confirm", "Confirmed"),
            ("checkin", "Checked In"),
            ("checkout", "Checked Out"),
            ("done", "Locked"),
            ("cancel", "Cancelled"),
        ],
        string="Status",
        required=True,
        readonly=True,
        index=True,
        copy=False,
        default="draft")

    expected_duration = fields.Integer(
        string="Expected stay duration (Days)", default=1, copy=True)

    sale_order_ids = fields.One2many(
        "sale.order",
        "reservation_id",
        string="Sale Order",
        copy=False,
        ondelete="cascade")
    order_count = fields.Integer(string="Order Count", compute="_get_order_count")
    total_adv_paid = fields.Float(string="Advance Payed", compute="_get_total_adv_paid", default=0.0)
    total_amount = fields.Float(string="Total Amount", compute="_get_total_amount", default=0.0)

    @api.depends("sale_order_ids.invoice_ids.amount_total_signed", 
                "sale_order_ids.invoice_ids.amount_residual_signed",
                "sale_order_ids.amount_total",
                "advance_payment_ids.adv_pay_unit",
                "advance_payment_ids.state",
                "room_ids.price_unit")
    def _get_total_amount_paid(self):
        for rec in self:
            total_paid = 0.0
            
            # First check if there are any invoices
            if rec.sale_order_ids.mapped('invoice_ids'):
                # Calculate from invoices
                invoice_payments = sum(
                    rec.sale_order_ids.mapped("invoice_ids.amount_total_signed")
                ) - sum(rec.sale_order_ids.mapped("invoice_ids.amount_residual_signed"))
                total_paid += invoice_payments

            
            # Add advance payments (only count posted payments)
            advance_payments = sum(
                rec.advance_payment_ids.filtered(
                    lambda p: p.state == 'post'
                ).mapped("adv_pay_unit")
            )
            total_paid += advance_payments
            
            rec.total_amount_paid = total_paid

    total_amount_paid = fields.Float(string="Amount Paid", compute="_get_total_amount_paid", default=0.0)
    total_due = fields.Float(string="Total Due", compute="_get_total_due", default=0.0)
    room_reservation_ids = fields.One2many(
        "hotel.room.reservation", "reservation_id", check_company=True)
    service_ids = fields.One2many(
        "hotel.service.reservation", "reservation_id", check_company=True)
    amenities_ids = fields.One2many(
        "hotel.amenities.reservation", "reservation_id", check_company=True)
    advance_payment_ids = fields.One2many(
        "advance.payment", "reservation_id", check_company=True)

    active = fields.Boolean(string="Active", default=True, tracking=True)
    invoice_count = fields.Integer(string="Invoice Count", compute="_get_invoice_count")

    salesperson = fields.Many2one(
        "res.users", string="Salesperson", tracking=True, default=lambda self: self.env.user)
    split_quotation = fields.Boolean(
        default=True, string="Split Quotation", required=True
    )

    display_quotation_option = fields.Boolean(
        compute="_display_quotation_option", store=True
    )

    payment_status = fields.Selection(
        [
            ("not_paid", "Not Paid"),
            ("partially_paid", "Partially Paid"),
            ("fully_paid", "Fully Paid"),
            ("cancel", "Cancelled"),
        ],
        string="Payment Status",
        compute="_get_payment_status",
        store=True,
    )

    invoice_ids = fields.Many2many(
        "account.move",
        string="Invoices",
        compute="_compute_invoices",
        store=True,
    )

    @api.depends("sale_order_ids.invoice_ids")
    def _compute_invoices(self):
        for rec in self:
            rec.invoice_ids = rec.sale_order_ids.mapped("invoice_ids")

    @api.depends("room_ids")
    def _get_room_display_html(self):
        for record in self:
            room_names = ""
            for room in record.room_ids:
                if room.room_id:
                    room_names += (
                        "<div style='border:2px solid #777777;display: inline-block;"
                        "margin: 2px;padding: 0px 5px;border-radius: 25px;'>"
                        f"{room.room_id.name}</div>"
                    )
            record.room_display_html = room_names

    @api.depends("invoice_ids.payment_state", "state")
    def _get_payment_status(self):
        for rec in self:
            # If reservation is cancelled, set payment status to cancel
            if rec.state == 'cancel':
                rec.payment_status = "cancel"
            # If no invoices exist
            elif not rec.invoice_ids:
                rec.payment_status = "not_paid"
            # Check invoice payment states
            elif all(inv.payment_state == "paid" for inv in rec.invoice_ids):
                rec.payment_status = "fully_paid"
            elif any(inv.payment_state in ["partial", "in_payment"] for inv in rec.invoice_ids):
                rec.payment_status = "partially_paid"
            else:
                rec.payment_status = "not_paid"

    @api.depends("invoice_ids.amount_residual", 
                "invoice_ids.amount_total",
                "sale_order_ids.amount_total",
                "sale_order_ids.state",
                "room_ids.price_unit",
                "advance_payment_ids.adv_pay_unit",
                "advance_payment_ids.state")
    def _get_total_due(self):
        for rec in self:
            total_due = 0.0
            
            # First check if there are any invoices
            if rec.invoice_ids:
                # Calculate from invoices
                total_due = sum(rec.invoice_ids.mapped("amount_residual"))
                
            # If no invoices but sales orders exist
            elif rec.sale_order_ids:
                # Calculate from sales orders (excluding cancelled and draft)
                total_due = sum(
                    rec.sale_order_ids.filtered(
                        lambda so: so.state not in ['cancel']
                    ).mapped("amount_total")
                )
                
            # If neither invoices nor sales orders exist
            else:
                # Calculate from reservation lines
                total_due = sum(rec.room_ids.mapped("price_unit"))
            
            # Subtract advance payments (only count posted payments)
            advance_payments = sum(
                rec.advance_payment_ids.filtered(
                    lambda p: p.state == 'post'
                ).mapped("adv_pay_unit")
            )
            total_due -= advance_payments
            
            # Ensure total_due is not negative
            rec.total_due = max(0.0, total_due)

    @api.depends("sale_order_ids")
    def _get_order_count(self):
        for rec in self:
            rec.order_count = len([
                order for order in rec.sale_order_ids if order.state != "cancel"
            ])
            _logger.info(f"Order_count:: {rec.order_count}")

    @api.depends("advance_payment_ids")
    def _get_total_adv_paid(self):
        for rec in self:
            rec.total_adv_paid = sum(rec.advance_payment_ids.mapped("adv_pay_unit"))

    @api.depends("sale_order_ids")
    def _get_total_amount(self):
        for rec in self:
            # Filter out cancelled sales orders before calculating total
            valid_orders = rec.sale_order_ids.filtered(lambda so: so.state != 'cancel')
            rec.total_amount = sum(valid_orders.mapped("amount_total"))

    @api.depends("company_id")
    def _display_quotation_option(self):
        _logger.info("Inside display quotation option")
        config = self.env["res.config.settings"].sudo()
        vals = config.get_values()
        split_quotation = vals.get("split_quotations", "")
        for rec in self:
            if split_quotation == "optional":
                rec.display_quotation_option = True
            else:
                rec.display_quotation_option = False
                if split_quotation == "split":
                    rec.split_quotation = True
                elif split_quotation == "combine":
                    rec.split_quotation = False

    @api.depends("company_id")
    def _default_hotel(self):
        hotel_id = self.env["hotel.hotel"].search([("is_default", "=", True)], limit=1).id
        return hotel_id

    hotel_id = fields.Many2one(
        "hotel.hotel",
        string="Hotel",
        default=_default_hotel,
        store=True,
        check_company=True,
        required=True,
    )

    @api.depends('company_id')
    def _multiple_hotel(self):
        for rec in self:
            # Convert string parameter to boolean
            multi_hotel_param = self.env["ir.config_parameter"].sudo().get_param("hotel_core.enable_multi_hotel")
            rec.multiple_hotel = multi_hotel_param == 'True' or multi_hotel_param == '1'

    multiple_hotel = fields.Boolean(
        "Multiple Hotel",
        compute=_multiple_hotel,
        required=True,
        store=False,
    )

    def unlink(self):
        for res in self:
            raise ValidationError("You cannot delete any record! Cancel the reservation instead!")

    def get_fy_name(self, date):
        config = self.env["res.config.settings"].sudo()
        vals = config.get_values()
        fiscal_year_book = vals.get("enable_fiscal_year_suffix_book", "")
        fy_name = ""
        if fiscal_year_book:
            if date:
                fy_record = self.env["fy.record"].with_context(active_test=False).search([
                    ("first_day", "<=", date),
                    ("last_day", ">=", date)
                ], limit=1)
            else:
                fy_record = self.env["fy.record"].search([("active", "=", True)], limit=1)

            if fy_record:
                fy_name += f"-{fy_record.name}"
        return fy_name

    @api.model_create_multi
    def create(self, vals_list):
        if not isinstance(vals_list, list):
            vals_list = [vals_list]
            
        for vals in vals_list:
            if "company_id" in vals:
                self = self.with_company(vals["company_id"])

            vals["booked_by"] = self.env.uid

            if vals.get("name", _("New")) == _("New"):
                seq_date = None
                if "booking_date" in vals:
                    seq_date = fields.Datetime.context_timestamp(
                        self, fields.Datetime.to_datetime(vals["booking_date"])
                    )
                vals["name"] = self.env["ir.sequence"].next_by_code(
                    "hotel.reservation", sequence_date=seq_date
                ) or _("New")

                fy_name = self.get_fy_name(vals.get("booking_date"))
                if fy_name:
                    vals["name"] += fy_name

            if "possible_checkout_date" not in vals and "possible_checkin_date" in vals:
                booking_date = fields.Datetime.to_datetime(vals["possible_checkin_date"])
                vals["possible_checkout_date"] = booking_date + timedelta(days=vals.get("expected_duration", 1))

        return super(HotelReservation, self).create(vals_list)

    @api.constrains(
        "possible_checkin_date", "booking_date", "confirmed_date",
        "checkin_date", "checkout_date", "possible_checkout_date"
    )
    def validate_dates(self):
        for rec in self:
            if rec.possible_checkin_date and rec.possible_checkout_date:
                if rec.possible_checkin_date > rec.possible_checkout_date:
                    raise ValidationError("Possible check-in date is after check-out date.")

            if rec.booking_date and rec.confirmed_date:
                if rec.booking_date > rec.confirmed_date:
                    raise ValidationError("Booking date is after confirmation date.")

            if rec.confirmed_date and rec.checkin_date:
                if rec.confirmed_date > rec.checkin_date:
                    raise ValidationError("Confirmed date is after check-in date.")

            if rec.checkin_date and rec.checkout_date:
                if rec.checkin_date > rec.checkout_date:
                    raise ValidationError("Check-in date is after check-out date.")

    def action_confirm_reservation(self):
        for rec in self:
            if not rec.room_ids:
                raise ValidationError("No room selected to book!")
            for room in rec.room_ids:
                if room.room_id.active and room.room_id.status in ["available", "hold"]:
                    room.room_id.status = "book"
                else:
                    raise ValidationError(f"Sorry! {room.room_id.name} is not available.")
            rec.state = "confirm"
            rec.confirmed_by = self.env.uid
            rec.confirmed_date = datetime.now()

    def action_checkin_reservation(self):
        for rec in self:
            if not rec.room_ids:
                raise ValidationError("No room selected to check in!")
            for room in rec.room_ids:
                if room.room_id.active and room.room_id.status not in ["occupy", "maintenance", "hold"]:
                    room.room_id.status = "occupy"
                else:
                    raise ValidationError(f"Sorry! {room.room_id.name} is not available for check-in.")
            rec.state = "checkin"
            rec.checkin_date = datetime.now()

    def action_checkout_reservation(self):
        for rec in self:
            if not rec.room_ids:
                raise ValidationError("No room selected to check out!")
            for room in rec.room_ids:
                if not room.isCheckedOut:
                    if room.room_id.active and room.room_id.status == "occupy":
                        room.room_id.status = "available"
                    else:
                        raise ValidationError(f"Room {room.room_id.name} is not in 'occupy' status.")
                    room.checkout_date = datetime.now()
                    room.status = "unassigned"
                    room.isCheckedOut = True
            rec.state = "checkout"
            rec.checkout_by = self.env.uid
            rec.checkout_date = datetime.now()

    def action_lock_reservation(self):
        for rec in self:
            if not rec.room_ids:
                raise ValidationError("No room selected to lock reservation!")

            for order_id in rec.sale_order_ids:
                if order_id.state not in ("cancel", "sale"):
                    raise ValidationError(
                        f"Sales order {order_id.name} is not finalized. Process it before locking the reservation."
                    )

            for room in rec.room_ids:
                room.status = "unassigned"

            rec.state = "done"
            rec.is_locked = True
            rec.locked_date = datetime.now()

    def cancel_reservation(self):
        for rec in self:
            # Check if there are any sales orders or invoices
            has_sales_orders = bool(rec.sale_order_ids)
            has_invoices = bool(rec.sale_order_ids.mapped('invoice_ids'))
            
            # Only cancel advance payments if no sales orders or invoices exist
            if not (has_sales_orders or has_invoices):
                for advance_payment in rec.advance_payment_ids.filtered(lambda p: p.state == 'post'):
                    if advance_payment.payment_id:
                        # Cancel the payment
                        advance_payment.payment_id.action_cancel()
                        # Update advance payment state
                        advance_payment.write({
                            'state': 'cancel',
                            'cancelled_by': self.env.uid,
                            'cancelled_date': datetime.now()
                        })

            # Then handle sales orders
            for order_id in rec.sale_order_ids:
                if order_id.state in ["done", "sale"]:
                    raise ValidationError(
                        f"Reservation has a processed sales order ({order_id.name}). Cancel it first."
                    )
                for invoice in order_id.invoice_ids.filtered(lambda inv: inv.state == "posted"):
                    raise ValidationError(
                        f"Reservation has posted invoice ({invoice.name}). Cancel it first."
                    )
                order_id._action_cancel(rec.cancellation_reason)

            # Update room status
            if rec.state in ["cancel", "book", "confirm", "draft", "checkin"]:
                for room in rec.room_ids:
                    if room.room_id.active and room.room_id.status != "maintenance":
                        room.room_id.status = "available"
                    room.status = "unassigned"

            # Finally update reservation state
            rec.state = "cancel"
            rec.cancelled_by = self.env.uid
            rec.cancelled_date = datetime.now()

    def action_cancel_reservation(self):
        for rec in self:
            if rec.sale_order_ids:
                for order_id in rec.sale_order_ids:
                    if order_id.state in ["done", "sale"]:
                        raise ValidationError(
                            "Sorry !! This reservation can't be cancelled. Some sales order has already been processed."
                            + " Please cancel order "
                            + order_id.name
                            + " manually, then cancel reservation."
                        )

            if len(self.ids) == 1:
                return {
                    "type": "ir.actions.act_window",
                    "name": "Reason For Cancelling",
                    "view_mode": "form",
                    "res_model": "hotel.reservation",
                    "view_id": self.env.ref("hotel_core.reservation_cancel_reason").id,
                    "views": [
                        (
                            self.env.ref("hotel_core.reservation_cancel_reason").id,
                            "form",
                        )
                    ],
                    "target": "new",
                    "res_id": self.id,
                }
            else:
                raise ValidationError("Sorry!! Bulk Cancellation is not allowed")

    def action_draft_reservation(self):
        for rec in self:
            if not rec.room_ids:
                raise ValidationError("No room selected!")
            for room in rec.room_ids:
                if room.room_id.active and room.room_id.status not in ["maintenance"]:
                    room.room_id.status = "available"

            rec.state = "draft"

    def prep_line_val_rooms(self):
        order_lines = []
        for room in self.room_ids:
            order_line = {
                "product_id": room.product_id.id,
                "product_uom_qty": room.actual_duration,
                "price_unit": room.price_unit,
                "discount": room.discount_percentage,
                "room_line_id": room.id,
                "reservation_id": self.id,
            }
            order_lines.append([0, 0, order_line])
        return order_lines

    def prepare_vals_for_rooms(self):
        return {
            "date_order": self.checkin_date,
            "user_id": self.env.uid,
            "partner_id": self.partner_id.id,
            "hotel_id": self.hotel_id.id,
            "hotel_order_type": "room",
            "order_line": self.prep_line_val_rooms(),
            "company_id": self.env.company.id,
            "reservation_id": self.id,
        }

    def prep_line_val_amenities(self):
        order_lines = []
        for amenity in self.amenities_ids:
            order_line = {
                "product_id": amenity.product_id.id,
                "product_uom_qty": amenity.qty,
                "price_unit": amenity.price_unit,
                "discount": amenity.discount_percentage,
                "amenity_line_id": amenity.id,
                "reservation_id": self.id,
            }
            order_lines.append([0, 0, order_line])

        return order_lines

    def prepare_vals_for_amenities(self):
        return {
            "date_order": self.checkin_date,
            "user_id": self.env.uid,
            "partner_id": self.partner_id.id,
            "hotel_id": self.hotel_id.id,
            "hotel_order_type": "amneity",
            "order_line": self.prep_line_val_amenities(),
            "company_id": self.env.company.id,
            "reservation_id": self.id,
        }

    def prep_line_val_services(self):
        order_lines = []
        for service in self.service_ids:
            order_line = {
                "product_id": service.product_id.id,
                "product_uom_qty": service.qty,
                "price_unit": service.price_unit,
                "discount": service.discount_percentage,
                "service_line_id": service.id,
                "reservation_id": self.id,
            }
            order_lines.append([0, 0, order_line])

        return order_lines

    def prepare_vals_for_services(self):
        return {
            "date_order": self.checkin_date,
            "user_id": self.env.uid,
            "partner_id": self.partner_id.id,
            "order_line": self.prep_line_val_services(),
            "hotel_id": self.hotel_id.id,
            "hotel_order_type": "service",
            "company_id": self.env.company.id,
            "reservation_id": self.id,
        }

    def action_update_sales_order(self):
        _logger.info("Inside update sales order")
        """
            Fetch existing sales order
            - If sales order not present or already processed then create a new sales order
            - No, update sales order line to relfect the quantity, unit price and discount
        
        """
        orders = []
        # Create Sales order for rooms
        if self.room_reservation_ids:
            # Check if order policy is split of combine

            if self.split_quotation:
                sales_order = self.env["sale.order"].search(
                    [
                        ("reservation_id", "=", self.id),
                        ("state", "not in", ["cancel", "done", "sale"]),
                        ("hotel_order_type", "=", "room"),
                    ]
                )
                _logger.info(sales_order)

                # Create/Update Sales order for Rooms
                if sales_order and len(sales_order) == 1:
                    _logger.info("Inside update")
                    order_line = {"order_line": self.prep_line_val_rooms()}

                    # Unlink order lines to re-create all the lines
                    for line in sales_order.order_line:
                        line.unlink()

                    room_sales_order = sales_order.update(order_line)
                else:
                    # Get order lines
                    _logger.info("Inside create")
                    # Prepare create value
                    vals = self.prepare_vals_for_rooms()
                    room_sales_order = sales_order.create(vals)

                orders.append(room_sales_order)

                # Create/Update Sales order for Amneties
                if self.amenities_ids:
                    # Create/Update based on whether a sales order has been created
                    sales_order = self.env["sale.order"].search(
                        [
                            ("reservation_id", "=", self.id),
                            ("state", "not in", ["cancel", "done", "sale"]),
                            ("hotel_order_type", "=", "amneity"),
                        ]
                    )

                    if sales_order and len(sales_order) == 1:
                        _logger.info("Inside update amneties order")
                        order_line = {"order_line": self.prep_line_val_amenities()}

                        # Unlink order lines to re-create all the lines
                        for line in sales_order.order_line:
                            line.unlink()
                        amneties_sales_order = sales_order.update(order_line)
                    else:
                        _logger.info("Inside create amneties order")
                        vals = self.prepare_vals_for_amenities()
                        amneties_sales_order = sales_order.create(vals)

                    orders.append(amneties_sales_order)

                # Create Sales order for Services
                if self.service_ids:
                    # Create/Update based on whether a sales order has been created
                    sales_order = self.env["sale.order"].search(
                        [
                            ("reservation_id", "=", self.id),
                            ("state", "not in", ["cancel", "done", "sale"]),
                            ("hotel_order_type", "=", "service"),
                        ]
                    )

                    _logger.info("Service sales order:: %s", sales_order)

                    if sales_order and len(sales_order) == 1:
                        _logger.info("Inside create service order")
                        order_line = {"order_line": self.prep_line_val_services()}

                        # Unlink order lines to re-create all the lines
                        for line in sales_order.order_line:
                            line.unlink()

                        services_sales_order = sales_order.update(order_line)
                    else:
                        _logger.info("Inside create service order")
                        vals = self.prepare_vals_for_services()
                        services_sales_order = sales_order.create(vals)

                    orders.append(services_sales_order)

            else:
                sales_order = self.env["sale.order"].search(
                    [
                        ("reservation_id", "=", self.id),
                        ("state", "not in", ["cancel", "done", "sale"]),
                        ("hotel_order_type", "=", "comb"),
                    ]
                )

                # Add a check here
                if sales_order and len(sales_order) == 1:
                    order_line = {
                        "order_line": self.prep_line_val_rooms()
                        + self.prep_line_val_amenities()
                        + self.prep_line_val_services()
                    }
                    sales_order.update(order_line)
                else:
                    sales_order = self.action_combine_quotation()

                orders.append(sales_order)

        return orders

    def action_combine_quotation(self):
        combined_order = {
            "date_order": self.checkin_date,
            "user_id": self.env.uid,
            "partner_id": self.partner_id.id,
            "order_line": self.prep_line_val_rooms()
                        + self.prep_line_val_amenities()
                        + self.prep_line_val_services(),
            "hotel_id": self.hotel_id.id,
            "hotel_order_type": "comb",
            "reservation_id": self.id,
        }
        return self.env["sale.order"].create(combined_order)

    def action_split_quotation(self):
        orders = []
        sales_order = self.env["sale.order"]
        if self.room_reservation_ids:
            orders.append(sales_order.create(self.prepare_vals_for_rooms()))
        if self.amenities_ids:
            orders.append(sales_order.create(self.prepare_vals_for_amenities()))
        if self.service_ids:
            orders.append(sales_order.create(self.prepare_vals_for_services()))
        return orders

    def action_create_sales_order(self):
        _logger.info("Inside create sales order")
        for reservation in self:
            """
            Orders for restaurant are always split by default.
            Hence only other orders are split/combined
            """

            if not self.checkin_date:
                raise ValidationError("Guest hasn't checked-in. Cannot generate sales order.")

            if self.split_quotation:
                return self.action_split_quotation()
            else:
                return self.action_combine_quotation()

    def action_view_hotel_invoice(self):
        """
        Open invoices related to the hotel reservation
        Returns a view with all invoices linked to the sales orders of this reservation
        """
        self.ensure_one()
        # Get all invoices from related sales orders
        invoices = self.mapped('sale_order_ids.invoice_ids').filtered(lambda inv: inv.state != 'cancel')
        
        # Get the action from the account module
        action = self.env["ir.actions.actions"]._for_xml_id("account.action_move_out_invoice_type")
        
        if len(invoices) > 1:
            # If multiple invoices, show them in a list view
            action['domain'] = [('id', 'in', invoices.ids)]
        elif len(invoices) == 1:
            # If single invoice, open form view directly
            form_view = [(self.env.ref('account.view_move_form').id, 'form')]
            action['views'] = form_view + [(state, view) for state, view in action.get('views', []) if view != 'form']
            action['res_id'] = invoices.id
        else:
            # No invoices found
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Invoices'),
                    'message': _('No invoices found for this reservation.'),
                    'sticky': False,
                    'type': 'warning',
                }
            }
        
        # Add context to ensure proper defaults if creating a new invoice
        context = {
            'default_move_type': 'out_invoice',
            'default_partner_id': self.partner_id.id,
            'default_invoice_origin': self.name,
            'default_hotel_id': self.hotel_id.id,
            'default_user_id': self.salesperson.id,
        }
        action['context'] = context
        
        return action

    def action_view_hotel_reservation_orders(self):
        return {
            "name": _("Sales Orders"),
            "view_mode": "list,form",
            "res_model": "sale.order",
            "search_view_id": [self.env.ref("sale.sale_order_view_search_inherit_quotation").id],
            "type": "ir.actions.act_window",
            "context": dict(self._context, create=False),
            "domain": [
                ("reservation_id", "=", self.id),
                ("state", "!=", "cancel"),
            ],
        }

    def action_view_total_amount(self):
        return

    def add_pos_transfer(self, amount, order_ref=None):
        """Add POS transfer amount as advance payment"""
        self.ensure_one()

        # Get or create a POS transfer payment method
        pos_payment_method = self.env['hotel.payment.method'].search([
            ('payment_type', '=', 'POS Transfer'),
            ('company_id', '=', self.company_id.id)
        ], limit=1)

        if not pos_payment_method:
            # Fallback to Cash payment method if POS Transfer doesn't exist
            pos_payment_method = self.env['hotel.payment.method'].search([
                ('payment_type', '=', 'Cash'),
                ('company_id', '=', self.company_id.id)
            ], limit=1)

        if not pos_payment_method:
            # If no payment method found, create a basic one
            # First get a default journal
            journal = self.env['account.journal'].search([
                ('type', '=', 'cash'),
                ('company_id', '=', self.company_id.id)
            ], limit=1)
            if not journal:
                journal = self.env['account.journal'].search([
                    ('type', '=', 'bank'),
                    ('company_id', '=', self.company_id.id)
                ], limit=1)

            pos_payment_method = self.env['hotel.payment.method'].create({
                'payment_type': 'POS Transfer',
                'company_id': self.company_id.id,
                'journal_id': journal.id if journal else False,
            })

        # Create an advance payment record for the POS transfer
        self.env['advance.payment'].create({
            'reservation_id': self.id,
            'adv_pay_unit': amount,
            'amount_to_pay': amount,
            'payment_type': pos_payment_method.id,
            'state': 'draft',  # Start as draft, will be posted by the system
        })

        # Log the transfer in chatter
        message = f"POS Order transferred: ${amount:.2f}"
        if order_ref:
            message += f" (Order: {order_ref})"
        self.message_post(body=message)

        return True

    def action_adv_pay(self):
        room = self.env["hotel.room.reservation"].search([("reservation_id", "=", self.id)], limit=1)
        amount_to_pay = self.total_due if self.order_count != 0 else room.net_price_unit
        return {
            "type": "ir.actions.act_window",
            "name": "Advance Payment",
            "view_mode": "form",
            "res_model": "advance.payment",
            "view_id": self.env.ref("hotel_core.action_hotel_adv_payment").id,
            "views": [(self.env.ref("hotel_core.view_hotel_adv_payment").id, "form")],
            "target": "new",
            "context": {
                "default_reservation_id": self.id,
                "default_amount_to_pay": amount_to_pay,
            },
        }

    def _get_invoice_count(self):
        for res in self:
            # Get all invoices from linked sale orders
            invoices = res.sale_order_ids.mapped('invoice_ids')
            # Count only valid invoices (not cancelled)
            res.invoice_count = len(invoices.filtered(lambda inv: inv.state != 'cancel'))

    def action_view_payments(self):
        """
        Open payments related to the hotel reservation's invoices and advance payments
        Returns a view with all payments linked to this reservation's invoices and advance payments
        """
        self.ensure_one()
        
        # Get all invoices from related sales orders
        invoices = self.mapped('sale_order_ids.invoice_ids').filtered(lambda inv: inv.state != 'cancel')
        
        # Get all payments linked to these invoices
        payments = self.env['account.payment']
        
        # Find payments through invoice reconciliation
        for invoice in invoices:
            for line in invoice.line_ids.filtered(lambda l: l.account_id.account_type in ('asset_receivable', 'liability_payable')):
                # Get payments through partial reconciliations
                for partial in line.matched_credit_ids | line.matched_debit_ids:
                    # Check credit move line
                    if partial.credit_move_id.payment_id:
                        payments |= partial.credit_move_id.payment_id
                    # Check debit move line
                    if partial.debit_move_id.payment_id:
                        payments |= partial.debit_move_id.payment_id
        
        # Also add payments that reference the invoice directly
        domain_payments = [
            ('memo', 'like', self.name),
            ('partner_id', '=', self.partner_id.id),
            ('state', '!=', 'cancelled')
        ]
        direct_payments = self.env['account.payment'].search(domain_payments)
        payments |= direct_payments
        
        # Get advance payments
        advance_payments = self.env['account.payment'].search([
            ('id', 'in', self.advance_payment_ids.mapped('payment_id').ids),
            ('state', '!=', 'canceled')
        ])
        payments |= advance_payments
        
        # Get the action from the account module
        action = self.env["ir.actions.actions"]._for_xml_id("account.action_account_payments")
        
        if payments:
            action['domain'] = [('id', 'in', payments.ids)]
        else:
            action['domain'] = [('id', 'in', [])]
        
        # Set context for creating new payments
        ctx = {
            'default_payment_type': 'inbound',
            'default_partner_type': 'customer',
            'default_partner_id': self.partner_id.id,
            'default_amount': sum(invoices.filtered(lambda inv: inv.payment_state != 'paid').mapped('amount_residual')),
            'default_ref': self.name,
            'hotel_reservation_id': self.id,
        }
        
        action['context'] = ctx
        return action

    def action_view_advance_payments(self):
        """
        Open advance payments related to the hotel reservation
        Returns a view with all advance payments for this reservation
        """
        self.ensure_one()
        
        # Get all advance payments for this reservation
        advance_payments = self.advance_payment_ids
        
        # Get the action from the advance payment model
        action = {
            'name': _('Advance Payments'),
            'type': 'ir.actions.act_window',
            'res_model': 'advance.payment',
            'view_mode': 'list,form',
            'domain': [('id', 'in', advance_payments.ids)],
            'context': {
                'default_reservation_id': self.id,
                'default_partner_id': self.partner_id.id,
                'default_company_id': self.company_id.id,
                'create': False,
            }
        }
        
        # If there's only one advance payment, open it in form view
        if len(advance_payments) == 1:
            action.update({
                'view_mode': 'form',
                'res_id': advance_payments.id,
                'views': [(self.env.ref('hotel_core.view_hotel_adv_payment').id, 'form')],
            })
        else:
            action.update({
                'views': [
                    (self.env.ref('hotel_core.view_hotel_adv_payment_list').id, 'list'),
                    (self.env.ref('hotel_core.view_hotel_adv_payment').id, 'form')
                ],
            })
        
        return action
