# -*- coding: utf-8 -*-
from odoo import models, api


class PosSession(models.Model):
    _inherit = 'pos.session'

    @api.model
    def get_hotel_reservations(self):
        """Get active hotel reservations for POS transfer"""
        reservations = self.env['hotel.reservation'].search([
            ('state', 'in', ['confirm', 'checkin']),
        ])

        reservation_data = []
        for reservation in reservations:
            # Get room information from room_ids (One2many field)
            room_names = []
            room_types = []
            for room_line in reservation.room_ids:
                if room_line.room_id:
                    room_names.append(room_line.room_id.name)
                    # Get room type from hotel.room model
                    if hasattr(room_line.room_id, 'room_categ_id') and room_line.room_id.room_categ_id:
                        room_types.append(room_line.room_id.room_categ_id.name)

            room_number = ', '.join(room_names) if room_names else 'No Room'
            room_type = ', '.join(room_types) if room_types else 'Unknown'

            reservation_data.append({
                'id': reservation.id,
                'name': reservation.name,
                'guest_name': reservation.partner_id.name,  # partner_id instead of guest_id
                'room_number': room_number,
                'room_type': room_type,
                'check_in': reservation.possible_checkin_date.strftime('%Y-%m-%d') if reservation.possible_checkin_date else '',
                'check_out': reservation.possible_checkout_date.strftime('%Y-%m-%d') if reservation.possible_checkout_date else '',
                'total_amount': reservation.total_amount,
                'due_amount': reservation.total_due,  # total_due instead of due_amount
                'balance_amount': reservation.total_due,  # Use total_due as balance for now
                'state': reservation.state,
                'state_label': dict(reservation._fields['state'].selection).get(reservation.state),
                'adults': reservation.total_guests,  # total_guests instead of adults
                'children': 0,  # hotel_core doesn't have separate children field
            })

        return reservation_data

    @api.model
    def transfer_to_hotel_reservation(self, reservation_id, amount, order_ref=None):
        """Transfer POS order amount to hotel reservation as advance payment"""
        try:
            reservation = self.env['hotel.reservation'].browse(reservation_id)
            if not reservation.exists():
                return {'success': False, 'message': 'Reservation not found'}

            # Create advance payment for the POS transfer
            # Get or create a Cash payment method (most common for POS transfers)
            payment_method = self.env['hotel.payment.method'].search([
                ('payment_type', '=', 'Cash'),
                ('company_id', '=', reservation.company_id.id)
            ], limit=1)

            if not payment_method:
                # If no Cash method found, get any available payment method
                payment_method = self.env['hotel.payment.method'].search([
                    ('company_id', '=', reservation.company_id.id)
                ], limit=1)

            if not payment_method:
                return {'success': False, 'message': 'No payment method available'}

            # Create advance payment record
            advance_payment = self.env['advance.payment'].create({
                'reservation_id': reservation.id,
                'adv_pay_unit': amount,
                'amount_to_pay': amount,
                'payment_type': payment_method.id,
                'state': 'draft',
            })

            # Post the payment
            advance_payment.action_post_payment()

            # Log the transfer in chatter
            message = f"POS Order transferred: ${amount:.2f}"
            if order_ref:
                message += f" (Order: {order_ref})"
            reservation.message_post(body=message)

            return {
                'success': True,
                'message': f'Successfully transferred ${amount:.2f} to {reservation.name}',
                'reservation_name': reservation.name,
                'guest_name': reservation.partner_id.name,
                'new_due_amount': reservation.total_due,
                'new_balance': reservation.total_due
            }

        except Exception as e:
            return {'success': False, 'message': f'Error: {str(e)}'}
